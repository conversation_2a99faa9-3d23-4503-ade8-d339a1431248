// regis.js
var util = require('../../utils/util.js');
// 获取全局应用程序实例对象
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    sendcheck: '验证码',
    checkTimeOut: 59 * 1000,
    cell_phone: '',
    parkid:'',
    check_numer: '',
    userid: '',
    token: '',
    showModalStatus: false,
    random1: 0,
    random2: 0,
    random3: 0,
    random4: 0,
    random:[],
    checkcodes: '',
    hiddenloading:true
  },
  checkcode: function (e) {
    console.log('弹出框的pwd  ' + e.detail.value);
    let that = this;
    that.setData({
      checkcodes: e.detail.value
    });
  },
  click_sendcheck: function (e) {
    let currentStatu = e.currentTarget.dataset.statu;
    this.check(currentStatu)
  },
  check: function (status) {
    let that = this;
    if (that.data.cell_phone == null || that.data.cell_phone == "") {
      util.alertDialog('请输入手机号');
      return;
    }
    if (that.data.parkid == null || that.data.parkid == "") {
      util.alertDialog('请输入登录账号');
      return;
    }

    if (that.data.sendcheck.indexOf('秒') >= 0) {
      return;
    }
    if (status == 'close') {
      this.setData(
        {
          showModalStatus: false
        }
      );
      // let nowcode = that.data.random1 + '' + that.data.random2 + '' + that.data.random3 + '' + that.data.random4
      let randomstr = ''
      for (var i of that.data.random){
        randomstr+=i
      }
      console.log('randomstr:   ' + randomstr)
      if (that.data.checkcodes == randomstr) {
        // util.alertDialog('ok')
        that.setData({
          hiddenloading:false
        })
        wx.request({
          url: app.globalData.url + '/user/reguser',
          data: {
            mobile: that.data.cell_phone,
            userid: that.data.parkid,
            ckey: randomstr
          },
          method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          }, // 设置请求的 header
          success: function (res) {
            if (res.statusCode != 200) {
              util.alertDialog(res.statusCode);
              return;
            }
            let state = res.data.state;
            switch (state) {
              case 0:
                util.alertDialog(res.data.errmsg);
               break;
              case -1:
                util.alertDialog('系统异常');
                break;
              case 2:
                util.alertDialog('手机号未登记');
                break;
              case 1:
                that.setData({
                  userid: res.data.userid
                })
                count_down(that, that.data.checkTimeOut);
                break;
              default:
                util.alertDialog(res.data.errmsg);
                break;
            }
          },
          fail: function (e) {
            // fail
            util.alertDialog('请求失败');
          },
          complete: function () {
            that.setData({
              hiddenloading: true
            })
            // console.log("登录完成")
          }
        })
      } else {
        util.alertDialog('请输入正确的验证码')
      }

    }
    if (status == 'open') {
      // that.setData({
      //   showModalStatus: true,
      //   random1: util.random(10),
      //   random2: util.random(10),
      //   random3: util.random(10),
      //   random4: util.random(10)
      // })
      that.setData({
        hiddenloading: false
      })
      wx.request({
        url: app.globalData.url + '/user/getckey',
        data: {
          mobile: that.data.cell_phone,
          userid: that.data.parkid,
        },
        method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        }, // 设置请求的 header
        success: function (res) {
          if (res.statusCode != 200) {
            util.alertDialog(res.statusCode);
            return;
          }

          // console.log('服务器验证码：' + util.decrypt(state))
          if(res.data.state==1){
            let state = util.decrypt(res.data.ckey);
            that.setData({
              showModalStatus: true,
              random: state.split('')
            })
          }else{
            // that.setData({
            //   showModalStatus: true,
            // })
            util.alertDialog(res.data.errmsg);
          }

        },
        fail: function (e) {
          // fail
          util.alertDialog('请求失败');
        },
        complete: function () {
          that.setData({
            hiddenloading: true
          })
          // complete
        }
      })
    }
  },
  click_next: function (e) {
    let that = this;
    if (that.data.cell_phone == null || that.data.cell_phone == "") {
      util.alertDialog('请输入手机号');
      return;
    }
    if (that.data.check_numer == null || that.data.check_numer == "") {
      util.alertDialog('请输入验证码');
      return;
    }
    that.setData({
      hiddenloading: false
    })
    wx.request({
      url: app.globalData.url + '/user/checkcode',
      data: {
        mobile: that.data.cell_phone,
        code: that.data.check_numer,
        userid: that.data.parkid
      },
      method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }, // 设置请求的 header
      success: function (res) {
        if (res.statusCode != 200) {
          util.alertDialog(res.statusCode);
          return;
        }
        let state = res.data.state;
        switch (state) {
          case -1:
            util.alertDialog('验证码过期');
            break;
          case 2:
            util.alertDialog('验证码错误');
            break;
          case 1:
            // that.setData({
            //   token: res.data.token
            // })
            let token = res.data.token
            let userid = res.data.userid
            wx.redirectTo({
              url: '../setpwd/setpwd?token=' + token + '&userid=' + userid
            })
            break;
        }
      },
      fail: function (e) {
        // fail
        util.alertDialog('请求失败');
      },
      complete: function () {
        // complete
        that.setData({
          hiddenloading: true
        })
      }
    })

  },
  input_cellphone: function (e) {
    let that = this;
    that.setData({
      cell_phone: e.detail.value
    })
  },
  input_check: function (e) {
    let that = this;
    that.setData({
      check_numer: e.detail.value
    })
  },
  input_parkid: function(e) {
    let that = this;
    that.setData({
      parkid: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let that = this;
    if (typeof (options.type) != 'undefined') {
      if (options.type == 'regis') {
        wx.setNavigationBarTitle({
          title: '新用户注册',
        })
      } else if (options.type == 'reset') {
        wx.setNavigationBarTitle({
          title: '找回密码',
        })
      }
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // let encode = util.encrypt('1234');
    // console.log('加密后：'+encode);
    // let decode = util.decrypt('E093EC096FF51EDADCF5BA5B05760F63');
    // console.log('解密后：'+decode);
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})

//用于倒计时
/* 毫秒级倒计时 */
function count_down(that, total_micro_second) {
  if (total_micro_second <= 0) {
    that.setData({
      sendcheck: "重新发送"
    });
    // timeout则跳出递归
    return;
  }

  // 渲染倒计时时钟
  that.setData({
    sendcheck: date_format(total_micro_second) + " 秒"
  });

  setTimeout(function () {
    // 放在最后--
    total_micro_second -= 1000;
    count_down(that, total_micro_second);
  }, 1000)



}

// 时间格式化输出，如03:25:19 86。每10ms都会调用一次
function date_format(micro_second) {
  // 秒数
  var second = Math.floor(micro_second / 1000);
  // 小时位
  var hr = Math.floor(second / 3600);
  // 分钟位
  var min = fill_zero_prefix(Math.floor((second - hr * 3600) / 60));
  // 秒位
  var sec = fill_zero_prefix((second - hr * 3600 - min * 60));// equal to => var sec = second % 60;
  // 毫秒位，保留2位
  var micro_sec = fill_zero_prefix(Math.floor((micro_second % 1000) / 10));

  return sec;
}

// 位数不足补零
function fill_zero_prefix(num) {
  return num < 10 ? "0" + num : num
}
