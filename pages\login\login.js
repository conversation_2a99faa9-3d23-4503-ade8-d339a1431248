var md5 = require('../../utils/md5.js');
var util = require('../../utils/util.js');
var Dec = require('../../utils/public.js'); //引用封装好的加密解密js
var rck = 'rememberCheck';
var rbFlag = false;
// 获取全局应用程序实例对象
var app = getApp();

function pwdVertify(account, password, that) {
  //  var password=value.password;
  if (account === "" || account === null) {
    alert("账号不能为空");
    return "";
  }
  if (password === "" || password === null) {
    alert("密码不能为空");
    return "";
  } else {
    that.passValue = Dec.Encrypt(password);
    var pwd = md5.hexMD5(md5.hexMD5(password) + "zldtingchebao201410092009");
    return pwd;
  }
}

function alert(message) {
  wx.showModal({
    title: '提示',
    content: message,
    confirmColor: '#118EDE',
    showCancel: true,

    success: function(res) {
      if (res.confirm) {
      }
    }
  });
}


// 创建页面实例对象
Page({
  /**
   * 页面名称
   */
  name: "index",
  /**
   * 页面的初始数据
   */

  data: {
    showModalStatus: false,
    loading: false,
    account: "",
    pwd: "",
    md5pwd: "",
    user: {},
    userid: '',
    imagesUrl: '',
    userValue: '',
    passValue: '',
    rememberCheck: false,
  },
  confirmpwd: function(e) {
  },
  powerDrawer: function(e) {
    var currentStatu = e.currentTarget.dataset.statu;
    this.util(currentStatu)
  },
  util: function(currentStatu) {
    //关闭
    if (currentStatu == "close") {
      this.setData({
        showModalStatus: false
      });
    }
    // 显示
    if (currentStatu == "open") {
      this.setData({
        showModalStatus: true
      });
    }
  },
  clickRegis: function(e) {
    wx.navigateTo({
      url: '../regis/regis?type=regis',
    })
  },
  clickReset: function(e) {
    wx.navigateTo({
      url: '../regis/regis?type=reset',
    })
  },
  loginbtn: function(e) {
    var that = this;
    var result = pwdVertify(that.data.account, that.data.pwd, this);

    if (result != "") {
      that.setData({
        loading: true,
        md5pwd: result
      })
      wx.request({
        url: app.globalData.url + '/user/dologin',
        data: {
          username: that.data.account,
          password: util.encrypt(that.data.pwd)
        },
        method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        }, // 设置请求的 header
        success: function(res) {
          // success
          let state = res.data.state;
          if (state) {
            //登录成功
            if (res.data.user.roleid == 4) {
              //角色是4才能登录
              let token = res.data.token;
              let user = res.data.user;
              app.globalData.user = res.data.user;
              that.setData({
                loading: false,
                user: res.data.user
              })
              wx.setStorageSync('username', that.data.account);
              wx.setStorageSync('password', that.data.pwd);
              wx.setStorageSync("passValue", that.passValue)
              wx.setStorageSync('token', token);
              wx.setStorageSync('user', user);
              wx.setStorageSync('unionid', res.data.user.unionid);
              wx.setStorageSync('parkid', res.data.user.parkid);
              wx.setStorageSync('ukey', res.data.user.ukey);
              if (res.data.user.paytype != undefined) {
                wx.setStorageSync('paytype', res.data.user.paytype);
              } else {
                wx.setStorageSync('paytype', 0);
              }
              // success
              wx.switchTab({
                url: '../index/index?source=login',
                success: function(res) {
                  // success
                },
                fail: function() {
                  // fail
                },
                complete: function() {
                  // complete
                }

              })
            } else {
              alert('角色没有权限！');
            }

          } else {
            //登录失败
            alert(res.data.msg);
            that.setData({
              loading: false
            })
          }
        },
        fail: function(e) {
          // fail
          console.log('login-error-->',e)
          util.network("登录失败，网络连接不可用")
          that.setData({
            loading: true
          })
        }
      })
    }

  },
  accountconfirm: function(e) {
    this.setData({
      account: e.detail.value
    })
  },
  //账号框
  accountinput: function(e) {
    this.setData({
      account: e.detail.value
    })
  },
  pwdconfirm: function(e) {
    this.setData({
      pwd: e.detail.value
    })
    pwdVertify(this.data.account, this.data.pwd, this)
  },
  //密码框
  pwdinput: function(e) {
    this.setData({
      pwd: e.detail.value
    })
  },
  checkboxChange: function(e) {
    var checked = e.detail.value[0] == '1'
    if (!checked) {
      rbFlag = false;
      wx.setStorageSync(rck, rbFlag);
    } else {
      rbFlag = true;
      wx.setStorageSync(rck, rbFlag);
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 注册coolsite360交互模块
    let that = this
    if (typeof(options.userid) != 'undefined') {
      that.setData({
        userid: options.userid
      })
    }
    try {
      rbFlag = wx.getStorageSync(rck);
      if (rbFlag) {
        this.setData({
          rememberCheck: true,
        })

        this.setData({
          userValue: wx.getStorageSync("username"),
          passValue: Dec.Decrypt(wx.getStorageSync("passValue")),
          account: wx.getStorageSync("username"),
          pwd: Dec.Decrypt(wx.getStorageSync("passValue")),
        })
      } else {
        this.setData({
          rememberCheck: false,
        })
      }
    } catch (e) {

    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 执行coolsite360交互组件展示
    // this.setData({
    //   account: "",
    //   pwd: ""
    // })
    util.network("网络连接不可用")
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },


  //以下为自定义点击事件


})