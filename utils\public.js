var aes = require('aes.js');  //引用AES源码js
var key = aes.CryptoJS.enc.Utf8.parse("1234123412ABCDEF");//十六位十六进制数作为秘钥
var iv = aes.CryptoJS.enc.Utf8.parse('ABCDEF1234123412');//十六位十六进制数作为秘钥偏移量
//解密方法
function Decrypt(word) {
  var encryptedHexStr = aes.CryptoJS.enc.Hex.parse(word);
  var srcs = aes.CryptoJS.enc.Base64.stringify(encryptedHexStr);
  var decrypt = aes.CryptoJS.AES.decrypt(srcs, key, { iv: iv, mode: aes.CryptoJS.mode.CBC, padding: aes.CryptoJS.pad.Pkcs7 });
  var decryptedStr = decrypt.toString(aes.CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}
//加密方法
function Encrypt(word) {
  var srcs = aes.CryptoJS.enc.Utf8.parse(word);
  var encrypted = aes.CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: aes.CryptoJS.mode.CBC, padding: aes.CryptoJS.pad.Pkcs7 });
  return encrypted.ciphertext.toString().toUpperCase();
}

//暴露接口
module.exports.Decrypt = Decrypt;
module.exports.Encrypt = Encrypt;