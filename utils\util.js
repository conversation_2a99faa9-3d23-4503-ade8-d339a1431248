var aes = require('../utils/aes.js')
//格式化时间 new Date(item.ctime*1000)
function formatTime(date) {
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()
  var hour = date.getHours()
  var minute = date.getMinutes()
  var second = date.getSeconds()
  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}
//格式化时间 不带日期
function formatTimeNoDay(date) {
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()
  var hour = date.getHours()
  var minute = date.getMinutes()
  var second = date.getSeconds()
  return [hour, minute, second].map(formatNumber).join(':')
}
function formatNumber(n) {
  n = n.toString()
  return n[1] ? n : '0' + n
}
//双击间隔
function oneSecondClick(times) {
  let nowtime = Date.now();
  // console.log('上次的时间是：'+times)
  // console.log('当前的时间是：'+nowtime)
  if (nowtime - times > 1000) {
    return true;
  } else {
    return false;
  }
}
//弹出提示框
function alertDialog(message) {
  wx.showModal({
    title: '提示',
    content: message,
    confirmColor: '#118EDE',
    showCancel: false,
    success: function (res) {
      if (res.confirm) {
        //console.log('用户点击确定')    
      }
    }
  });
}
//弹出提示框
function alertDialogBack(message) {
  wx.showModal({
    title: '提示',
    content: message,
    confirmColor: '#118EDE',
    showCancel: false,
    success: function (res) {
      if (res.confirm) {
        //console.log('用户点击确定')
        wx.navigateBack({
        })    
      }
    }
  });
}
function reLogin() {
  var rememberCheck = wx.getStorageSync('rememberCheck')
  if (!rememberCheck){
    wx.setStorageSync('username', '');
    wx.setStorageSync('password', '');
    wx.setStorageSync('token', '');
  }
  wx.redirectTo({
    url: '../login/login',
    success: function (res) {
      // success
    },
    fail: function () {
      // fail
    },
    complete: function () {
      // complete

    }
  })
}
function random(max){
  return (Math.floor(Math.random() * max));
}
/**
	 * [encrypt 加密 AES]
	 * @return {[type]} [description]
	 */
const key = aes.CryptoJS.enc.Utf8.parse("zldboink20170613");
function encrypt(content) {
  var encryptResult = aes.CryptoJS.AES.encrypt(content, key, {
    iv: key,
    mode: aes.CryptoJS.mode.CBC,
    padding: aes.CryptoJS.pad.Pkcs7
  });
  return encryptResult.toString();
}
/**
 * [decrypt 解密 AES]
 * @return {[type]} [description]
 */

function decrypt(content) {
  var bytes = aes.CryptoJS.AES.decrypt(content, key, {
    iv: key,
    mode: aes.CryptoJS.mode.CBC,
    padding: aes.CryptoJS.pad.Pkcs7
  });
  return bytes.toString(aes.CryptoJS.enc.Utf8);
}
/**
 * 判断当前网络状态
 * wx.getNetworkType
 *  返回网络类型, 有效值：
 *  wifi/2g/3g/4g/unknown(Android下不常见的网络类型)/none(无网络)
 * res.networkType
 */
function networkType(){
  wx.getNetworkType({
    success: function (res) {
      if (res.networkType == "none") {
        wx.showModal({
          title: '提示',
          content: '当前网络不可用,请检查网络设置',
          confirmColor: '#118EDE',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
            }
          }
        });
      } else {
        wx.showModal({
          title: '提示',
          content: '登录已过期，请重新登录',
          confirmColor: '#118EDE',
          showCancel: false,
          success: function (res) {
            reLogin()
          }
        });
      }

    }
  })
}

function network(message) {
  wx.getNetworkType({
    success: function (res) {
      if (res.networkType == "none") {
        wx.showModal({
          title: '提示',
          content: message,
          confirmColor: '#118EDE',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
            }
          }
        });
      }else{
        console.log('网络错误')
      }

    }
  })
}
//导出方法
module.exports = {
  formatTime: formatTime,
  oneSecondClick: oneSecondClick,
  alertDialog: alertDialog,
  alertDialogBack: alertDialogBack,
  reLogin: reLogin,
  formatTimeNoDay:formatTimeNoDay,
  random: random,
  encrypt: encrypt,
  decrypt: decrypt,
  networkType: networkType,
  network: network
}

