/* pages/aboutus/aboutus.wxss */

page {
  display: block;
  min-height: 100%;
  background-color: #eeedf3;
}
.views {
    width: 100%;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 50rpx 0;
  margin-top: 50rpx;
}

.image {
  width: 600rpx;
  height: 300rpx;
}

.views text {
  margin-top: 50rpx;
}


.commodity_screen {
 width: 100%;
 height: 100%;
 position: fixed;
 top: 0;
 left: 0;
 background: #000;
 opacity: 0.2;
 overflow: hidden;
 z-index: 1000;
 color: #fff;
}
.commodity_attr_box {
 width: 100%;
 overflow: hidden;
 position: fixed;
 bottom: 0;
 left: 0;
 z-index: 2000;
 background: #fff;
 padding-top: 20rpx;
}