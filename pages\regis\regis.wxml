<!--regis.wxml-->
<!--mask 遮罩-->
<view class="drawer_screen" bindtap="powerDrawer" data-statu="close" wx:if="{{showModalStatus}}"></view>

<!--content dialog-->
<view class="drawer_box" wx:if="{{showModalStatus}}">
  <!--<view class="drawer_box" wx:if="true">-->
  <!--drawer content-->
  <view class="drawer_title">输入下方验证码</view>
  <view class="drawer_content">
    <view class="checkview">
      <view class="checkitem">
        <text class="check1">{{random[0]}}</text>
      </view>
      <view class="seperate"></view>
      <view class="checkitem">
        <text class="check2">{{random[1]}}</text>
      </view>
      <view class="seperate"></view>
      <view class="checkitem">
        <text class="check3">{{random[2]}}</text>
      </view>
      <view class="seperate"></view>
      <view class="checkitem">
        <text class="check4">{{random[3]}}</text>
      </view>
    </view>
    <view class="top grid">
      <label class="title col-0"></label>
      <input class="input_base input_h30 col-1" name="rName" placeholder="" type="number" focus="true" bindinput="checkcode"></input>
    </view>
  </view>
  <view class="btn_ok" bindtap="click_sendcheck" data-statu="close">确定</view>
</view>
<loading hidden="{{hiddenloading}}" bindchange="loadingChange">
  加载中...
</loading>
<!--<view class="contains">
  <text>请输入手机号码:</text>
  <view class="view_phone">
    <input class="input_cellphone" type="number" placeholder="11位手机号码" bindinput="input_cellphone" focus="true" />
    <button class="btn_sendcheck" bindtap="click_sendcheck" data-statu="open">{{sendcheck}}</button>
  </view>
  <view class="view_phone">
    <text class="text_check">验证码：</text>
    <input class="input_check" type="number" placeholder="输入手机收到的验证码" bindinput="input_check" />
  </view>
  <button type="primary" class="btn_next" bindtap="click_next">下一步</button>
</view>-->
<view class="contains">



  <view class="view_phone">
    <text class="text_check">手机号码:</text>
    <input class="input_cellphone" type="number" placeholder="11位手机号码" bindinput="input_cellphone" focus="true" />
    <!--<button class="btn_sendcheck" bindtap="click_sendcheck" data-statu="open">{{sendcheck}}</button>-->
  </view>
  <view class="view_phone">
    <text class="text_check">登录账号：</text>
    <input class="input_parkid" type="number" placeholder="输入登录账号" bindinput="input_parkid" />
   
  </view>
  <view class="view_phone view_phone0">
    <text class="text_check">验 证 码：</text>
    <input class="input_check" type="number" placeholder="输入验证码" bindinput="input_check" />
     <button class="btn_sendcheck" bindtap="click_sendcheck" data-statu="open">{{sendcheck}}</button>
  </view>
  <button type="primary" class="btn_next" bindtap="click_next">下一步</button>
</view>