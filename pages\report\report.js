//index.js
//获取应用实例
var app = getApp()
var utils = require('../../utils/util.js');
var off = true;
Page({
  data: {
    scrollHeight: 0,
    scrollTop: 0,
    hidden: true,
    hiddenin: true,
    page: 1,
    size: 20,
    motto: 'Hello World1',
    userInfo: {},
    berths: [],
    info: [],
    result: null,
    orderNum: 0,
    orderTotal: 0,
    totalCount: 0,
    loadTime: 0,
    cashDetail: [
      {
        typeName: '自动提现',
        types: 2,
        yue: 0,
        date: '2017-4-1 12:45:22',
        cash: 2500
      },
      {
        typeName: '每日结算',
        types: 1,
        yue: 2500,
        date: '2017-4-1 00:00:22',
        cash: 500
      }
    ],
    isShowEmpty: false,
    currentTime: 0
  },
  //下拉刷新
  onPullDownRefresh: function () {

  },
  //滚动触发
  scroll: function (event) {
    // this.setData({
    //   scrollTop: event.detail.scrollTop
    // });
  },
  //滑动到顶部刷新
  scrolltop: function (e) {
    var that = this;
    that.setData({
      page: 1,
      info: [],
      // 2018/6/15 add
      isShowEmpty: false
    })
    if(off){
      loadData(20, 1, this);
    }
    
    // console.log("滑到到顶部了" + that.data.page);
  },
  clickFresh: function (e) {
    var that = this;
    that.setData({
      page: 1,
      info: []
    })
    loadData(this.data.size, this.data.page, this);
  },
  //滑动到底部触发加载更多
  scrollbottom: function (e) {
    let that = this;
    let timespan = new Date().getTime() - that.data.currentTime;
    that.data.currentTime = new Date().getTime();
    if(timespan>2000){
       if ((that.data.totalCount == that.data.info.length) && that.data.totalCount > 0) {

      } else {
        // 修改加载动画显示的位置
        that.setData({
          page: that.data.page + 1
        })
        if(off){
          loadData(this.data.size, this.data.page, this);
        }
        
      }
    }else{
      // console.log('小于两秒不执行任何事件')
    }

  },
  onLoad: function () {
    var that = this
    wx.getSystemInfo({
      success: function (res) {
        console.log('获取屏幕高度',res)
        // success
        that.setData({
          scrollHeight: res.windowHeight
        })
      }
    })
  },
  onShow:function(){
    wx.showLoading({
      title: '加载中...',
      mask: true,
    })
    this.setData({
      size: 20,
      page: 1,
      info: [],
    })
    loadData(20, 1, this);
  }

})

function loadData(size, page, that) {
  off = false;
  that.setData({
    hiddenin: false
  })
  wx.showLoading({
    title: '加载中...',
    mask: true,
  })
  let token;
  let parkid;
  wx.getStorage({
    key: 'token',
    success: function (res) {
      token = res.data;
      parkid = res.data;
      wx.request({
        url: app.globalData.url + '/trade/moneydayrecord',
        data: {
          page: page,
          rp: size,
          token: token,
          dtype: 3,
        },
        method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        }, // 设置请求的 header
        success: function (res) {
          // success
          if (res.statusCode == 200) {
            that.setData({
              // 2018/6/15 add
              isShowEmpty: true,
              hidden: true,
              hiddenin: true,
            })
            wx.hideLoading()
            if (res.data.validate != undefined) {
              utils.networkType()
            } else {
              that.data.totalCount = res.data.totalCount;
              if (that.data.totalCount > that.data.info.length) {
                let arrs = res.data.rows;
                let isNull;
                if (res.data.rows == 0) {
                  isNall = true;
                } else {
                  isNull = false;
                }
                for (let item of arrs) {
                  item.ctime = utils.formatTime(new Date(item.ctime * 1000))
                  item.total = item.total.toFixed(2)
                }
                that.setData({
                  info: that.data.info.concat(arrs),
                  loadTime: Date.now()
                });
              }
              if (res.data.rows.length == 0 && that.data.totalCount > 0) {
                wx.showToast({
                  title: '已经是最后一条记录',
                  icon: 'success',
                  duration: 2000
                })
              }
              if (res.data.total == 0) {
                  that.setData({
                    isShowEmpty: false
                  })
                } else {
                  that.setData({
                    isShowEmpty: true
                  })
                }
            }
            off = true;
          } else {
            utils.alertDialog('请求错误' + res.statusCode)
          }
        },
        fail: function (res) {
          // fail
          that.setData({
            hiddenin: true
          })
          wx.hideLoading()
          utils.networkType()
        },
        complete: function () {
          // complete
        }
      })

    },
    fail: function () {
      // fail
      utils.reLogin();
    },
    complete: function () {
      // complete
    }
  })
}
