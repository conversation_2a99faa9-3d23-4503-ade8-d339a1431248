// setpwd.js
var util = require('../../utils/util.js');
// 获取全局应用程序实例对象
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    token: '',
    userid:'',
    pwd:'',
    confirmpwd:''
  },
  setPass:function(e){
    let that = this;
    that.setData({
      pwd: e.detail.value
    })
  },
  confirmPass: function (e) {
    let that = this;
    that.setData({
      confirmpwd: e.detail.value
    })
  },
  click_ok:function(e){
    let that = this;
    if (that.data.pwd.length<6){
      util.alertDialog('密码长度不能小于6位')
      return
    }
    if (that.data.pwd != that.data.confirmpwd){
      util.alertDialog('两次密码输入不一致')
      return
    }
    wx.request({
      url: app.globalData.url + '/user/resetpwd',
      data: {
        token: that.data.token,
        passwd: that.data.pwd
      },
      method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }, // 设置请求的 header
      success: function (res) {
        if (res.statusCode != 200) {
          util.alertDialog(res.statusCode);
          return;
        }
        let state = res.data.state;
        switch (state) {
          case -1:
            util.alertDialog('密码设置失败');
            break;
          case 1:
            wx.redirectTo({
              url: '../login/login?userid='+that.data.userid,
            })
            break;
        }
      },
      fail: function (e) {
        // fail
        util.alertDialog('请求失败');
      },
      complete: function () {
        // complete
        console.log("登录完成")
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let that = this;
    if (typeof (options.token) != 'undefined') {
      that.setData({
        token: options.token
      })
    }
    if (typeof (options.userid) != 'undefined') {
      that.setData({
        userid: options.userid
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
  
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
  
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
  
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
  
  }
})