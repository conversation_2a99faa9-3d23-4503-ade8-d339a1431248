/* pages/cash/cash.wxss */

page {
  width: 100%;
  height: 100%;
  background: #f0eff5;
}
.loading{
  width: 100%;
  height: 100%;
}
.padd {
  padding: 0 40rpx;
  color: #f0eff5;
}

.radio {
  display: block;
  line-height: 40rpx;
  margin-top: 60rpx;
}

.radio span {
  display: inline-block;
  padding-top: 10rpx;
}

.middle {
  width: 85%;
  margin-left: 30rpx;
  padding-left: 20rpx;
  padding-top: 30rpx;
  border-top: 1px solid #eeedf3;
  border-bottom: 1px solid #eeedf3;
  height: 80rpx;
}

.middle__title {
  margin: 60rpx 30rpx 20rpx;
}

.middle span {
  float: left;
}

.middle label {
  float: right;
}

.inputview {
  margin-top: 50rpx;
  display: flex;
  height: 80rpx;
  align-items: center;
}

.inputview input {
  float: left;
  height: 70rpx;
  width: 200rpx;
  margin-left: 50rpx;
  padding-left: 20rpx;
  border: 1px solid #62b900;
}

.inputview text {
  float: left;
  margin-left: 10rpx;
}

.button {
  margin: 150rpx 40rpx;
}

/*以下是新页面设计*/

.contain {
  margin: 30rpx;
  background: white;
  height: 710rpx;
}

.containcard {
  background: #fbfbfb;
  height: 120rpx;
  padding-top: 30rpx;
  display: flex;
  flex-direction: row;
}

.text_banker_left {
  flex: 1.8;
  display: flex;
  justify-content: center;
  width: 0;
}

.text_banker {
  flex: 3;
  text-align: start;
  display: flex;
  flex-direction: column;
  width: 0;
  justify-content: flex-start;
}

 .text_banker text {
  color: #62688c;
  margin-left: 1rpx;
  margin-right: 1rpx;
  text-overflow: ellipsis;
  display: block;
  overflow: hidden;
  white-space: nowrap;
}  

/* .text_banker text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
} */

.containjine {
  height: 80rpx;
  padding-top: 30rpx;
  padding-left: 50rpx;
}

.containcash {
  height: 120rpx;
  padding-top: 30rpx;
  padding-left: 50rpx;
  font-size: 60rpx;
}

.containcash text {
  float: left;
  margin-top: 10rpx;
}

.containcash input {
  float: left;
  width: 450rpx;
  height: 100rpx;
  padding-left: 50rpx;
}

.border {
  background: #eeedf3;
  height: 1px;
  margin: 0 50rpx;
}

.containbutton {
  padding: 50rpx;
  text-align: center;
}

.containbutton text {
  font-size: 30rpx;
  color: #8e8e8e;
}

.containbutton button {
  margin: 20rpx;
}
