//index.js
//获取应用实例
var app = getApp()
var utils = require('../../utils/util.js');
var off = true;
Page({
  data: {
    scrollHeight: 0,
    scrollTop: 0,
    hidden: true,
    hiddenin: true,
    page: 1,
    size: 20,
    motto: 'Hello World1',
    userInfo: {},
    berths: [],
    info: [],
    result: null,
    orderNum: 0,
    orderTotal: 0,
    totalCount: 0,
    loadTime: 0,
    password: '',
    username: '',
    umpty: true,
    timespan: 0
  },
  //事件处理函数
  bindViewTap: function() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },
  //下拉刷新
  onPullDownRefresh: function() {

  },
  //滚动触发
  scroll: function(event) {

  },
  //滑动到顶部刷新
  scrolltop: function(e) {
    var that = this;
    that.setData({
      page: 1,
      info: [],
      hidden: false,
      umpty: false
    })
    wx.showLoading({
      title:'加载中...',
      mask:true
    })
    if(off){
      loadData(this.data.size, this.data.page, this);
    }
  },
  clickFresh: function(e) {
    var that = this;
    that.setData({
      page: 1,
      info: []
    })
    loadData(this.data.size, this.data.page, this);
  },
  //滑动到底部触发加载更多
  scrollbottom: function(e) {
    let that = this;
    let timespan = new Date().getTime() - that.data.currentTime;
    that.data.currentTime = new Date().getTime();
    if (timespan > 2000) {
      if ((that.data.totalCount == that.data.info.length) && that.data.totalCount > 0) {

      } else {
        that.setData({
          page: that.data.page + 1,
          hiddenin: false
        })
        wx.showLoading({
          title: '加载中...',
          mask: true
        })
        loadData(this.data.size, this.data.page, this);
      }
    } else {
      if (off) {
        that.setData({
          page: that.data.page + 1,
          hiddenin: false
        })
        wx.showLoading({
          title: '加载中...',
          mask: true
        })
        loadData(this.data.size, this.data.page, this);
      }

    }
  },
  onLoad: function(option) {
    if (option.source == 'login') {
      return;
    }

  },
  onShow: function() {
    wx.showLoading({
      title: '加载中...',
      mask: true,
    })
    let uname = wx.getStorageSync('username');
    let pwd = wx.getStorageSync('password');
    let that = this
    var query = wx.createSelectorQuery(); //选择id    
    query.select('#header').boundingClientRect(function(rect) {
      wx.getSystemInfo({
        success: function (res) {
          // success
          that.setData({
            scrollHeight: res.windowHeight - rect.height,
            username: uname,
            password: pwd,
          })
        }
      })
    }).exec();
    that.setData({
      size:20,
      page:1,
      info: [],
    })
    loadData(this.data.size, 1, this);
  }

})

function loadData(size, page, that) {
  off = false;
  that.setData({
    hidden: false
  })
  wx.showLoading({
    title: '加载中...',
    mask: true
  })
  let token = wx.getStorageSync('token');
  let parkid;

  wx.request({
    url: app.globalData.url + '/trade/moneytodayrecord',
    data: {
      page: page,
      rp: size,
      token: token,
    },
    method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
    header: {
      'content-type': 'application/x-www-form-urlencoded'
    }, // 设置请求的 header
    success: function(res) {
      off = true;
      that.setData({
        hidden: true,
        hiddenin: true,
        // 2018/6/15 add
        umpty: true
      })
      wx.hideLoading()
      if (res.data.validate != undefined) {
        utils.networkType()
      } else {
        let Count = res.data.totalCount;
        if (Count > that.data.info.length) {
          for (let row of res.data.rows) {
            row.end_time = utils.formatTimeNoDay(new Date(row.end_time * 1000))
          }
          let rows;
          if (that.data.page == 1) {
            rows = res.data.rows;
          } else {
            //增加去重
            rows = mergeData(that.data.info, res.data.rows); 
          }
          that.setData({
            page: res.data.page,
            orderNum: res.data.totalCount,
            totalCount: res.data.totalCount,
            orderTotal: res.data.money,
            info: rows,
            loadTime: Date.now()
          });
        }
        if (res.data.rows.length == 0 && that.data.totalCount > 0) {
          wx.showToast({
            title: '已经是最后一条记录',
            icon: 'success',
            duration: 2000
          })
        }
        if (res.data.totalCount == 0) {
          that.setData({
            umpty: false
          })
        } else {
          that.setData({
            umpty: true,
            totalCount: res.data.totalCount
          })
        }
      }
    },
    fail: function(res) {
      // fail
      that.setData({
        hidden: true
      })
      wx.hideLoading();
      utils.networkType()
    }
  })



}
/*
 *合并数据并去重
 */
function mergeData(oldData, newData) {
  var tempData = [];
  for (var i = 0; i < newData.length; i++) {
    var nItem = newData[i];
    var has = false;
    for (var j = 0; j < oldData.length; j++) {
      var oItem = oldData[j];
      if (nItem.id != oItem.id) {
        has = true;
      }
    }
    if (has) {
      tempData.push(nItem);
    }
  }
  return oldData.concat(tempData)

}
function reloginNow(that) {
  wx.request({
    url: app.globalData.url + '/user/dologin',
    data: {
      username: wx.getStorageSync('username'),
      password: utils.encrypt(wx.getStorageSync('password'))
    },
    method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
    header: {
      'content-type': 'application/x-www-form-urlencoded'
    }, // 设置请求的 header
    success: function(res) {
      // success
      let state = res.data.state;
      if (state) {
        //登录成功
        if (res.data.user.roleid == 4) {
          //角色是4才能登录
          let token = res.data.token;
          let user = res.data.user;
          app.globalData.user = res.data.user;
          that.setData({
            loading: false,
            user: res.data.user
          })
          wx.setStorageSync('token', token);
          wx.setStorageSync('user', user);
          wx.setStorageSync('unionid', res.data.user.unionid);
          wx.setStorageSync('parkid', res.data.user.parkid);
          wx.setStorageSync('ukey', res.data.user.ukey);
          if (res.data.user.paytype != undefined) {
            wx.setStorageSync('paytype', res.data.user.paytype);
          } else {
            wx.setStorageSync('paytype', 0);
          }
          // 重新登陆后拉取数据
          loadData(that.data.size, 1, that);
          // success
        } else {
          // alert('角色没有权限！');
          utils.reLogin();
          console.log("角色没有权限")
        }
      } else {
        //登录失败
        utils.reLogin();
        console.log("登录失败")
      }
    },
    fail: function(e) {
      // fail
      utils.networkType()
    }
  })
}