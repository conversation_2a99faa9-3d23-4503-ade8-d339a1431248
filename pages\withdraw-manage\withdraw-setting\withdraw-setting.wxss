/* pages/withdraw-manage/withdraw-setting/withdraw-setting.wxss */

page {
  height: 100%;
  background-color: #eeedf3;
}

.contain {
  display: flex;
  flex-direction: column;
  background: white;
  padding: 0 40rpx;
  margin-top: 40rpx;
}

.tips {
  font-size: 30rpx;
}

.setting-item {
  display: flex;
  flex-direction: row;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
  justify-content: space-around;
  align-items: center;
  line-height: 40rpx;
}
.setting-item text{
  flex: 1;
}
.input {
  border-bottom: 1px solid #62b900;
  padding:0 20rpx;
}
.button{
  width: 80%;
  margin-top: 50rpx;
}