// pages/withdraw-manage/withdraw-setting/withdraw-setting.js
var app = getApp();
var utils = require('../../../utils/util.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    hintperiod: '单位为天',
    hintmoney: '单位为元',
    periodvalue: '',
    moneyvalue: '',
    autocash: true,
    ischeck: true,
    loadinghidde:true
  },
  submitsetting: function () {
    let that = this;
    if (that.data.autocash && that.data.periodvalue == '' && that.data.moneyvalue == '') {
      utils.alertDialog('请输入提现周期或金额');
      return;
    }
    // if (that.data.periodvalue.length>9){
    //   utils.alertDialog('请输入提现周期或金额');
    //   return;
    // }
    that.setData({
      loadinghidde: false
    })
    wx.request({
      url: app.globalData.url + '/kftwithdraw/tosetup',
      data: {
        user_union_id: wx.getStorageSync('unionid'),
        user_park_id: wx.getStorageSync('parkid'),
        money: that.data.moneyvalue,
        cycle: that.data.periodvalue,
        checked: that.data.autocash ? 1 : 0,
        token: wx.getStorageSync('token')
      },
      method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }, // 设置请求的 header
      success: function (res) {
        that.setData({
          loadinghidde: true,
        })
        if(res.data.state){
          utils.alertDialogBack(res.data.errmsg);
        }else{
          utils.alertDialog(res.data.errmsg);
        }
        
      },
      fail:function(res){
        utils.network("网络连接不可用")
        that.setData({
          loading: false,
          loadinghidde:true
        })
      }
    })
  },
  switchwithdraw: function (e) {
    //切换自动提现
    console.log(e.detail.value)
    let that = this;
    that.setData({
      autocash: e.detail.value,
      periodvalue: e.detail.value ? that.data.periodvalue : '',
      moneyvalue: e.detail.value ? that.data.moneyvalue : '',
    })
  },
  inputperiod: function (e) {
    //提现周期
    console.log(e.detail.value)
    let that = this;
    if (that.data.autocash) {
      that.setData({
        periodvalue: e.detail.value,
        moneyvalue: ''
      })
    }
  },
  inputmoney: function (e) {
    //提现金额
    console.log(e.detail.value)
    let that = this;
    that.setData({
      moneyvalue: e.detail.value,
      periodvalue: ''
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let that = this;
    that.setData({
      loadinghidde: false
    })
    wx.request({
      url: app.globalData.url + '/park/autoWithdrawalMoney',
      data: {
        user_role_id: '4',
        user_union_id: wx.getStorageSync('unionid'),
        user_park_id: wx.getStorageSync('parkid'),
        token: wx.getStorageSync('token')
      },
      method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }, // 设置请求的 header
      success: function (res) {

        if (res.data.state == '1') {
          let period = res.data.withdrawal_cycle;
          let money = res.data.withdrawal_money;
          if (period == '0' && money == '0') {
            that.setData({
              loadinghidde: true,
              ischeck: false,
              autocash:false
            })
          } else {
            that.setData({
              loadinghidde: true,
              ischeck: true,

              periodvalue: period == '0' ? '' : period,
              moneyvalue: money == '0' ? '' : money,
            })
          }
        } else {
          utils.alertDialog(res.data.errmsg);
         
        }

      },
      fail:function(res){
        utils.network("网络连接不可用")
        that.setData({
          loadinghidde: true
        })
      },
      finally:function(){
        that.setData({
          loadinghidde: true,
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})