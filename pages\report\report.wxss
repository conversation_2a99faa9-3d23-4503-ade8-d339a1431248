/* pages/report/report.wxss */

/*.padd {
  padding: 10rpx;
}*/
.mask{
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9999
}
.flex-item {
  display: flex;
  direction: column;
  align-content: center;
  align-items: center;
}
.scrollview{
  padding-left: 5%;
  width: 95%
}
.line1 {
   width: 95%;
  margin-top: 30rpx;
  height: 60rpx;
}
.line2{
 width: 95%;
    height: 60rpx;
    border-bottom: 1px solid #62b900;
}
.texttype {
  float: left;
  margin-left: 50rpx;
}

.textmoney {
  float: right;
  color: #62b900;
}

.textyuan {
  float: right;
  margin-right: 85rpx;
}

.textyue {
  font-size: 30rpx;
  color: #7f7c7c;
  float: left;
  margin-left: 40rpx;
}

.texttime {
  font-size: 30rpx;
  color: #7f7c7c;
  float: right;
  margin-right: 60rpx;
}
.refresh{
  width: 100%;
  text-align: center;
  margin-top: 450rpx;
}