/* pages/me/me.wxss */

page {
  display: block;
  /* height: 100%; */
  background-color: #eeedf3;
}

.padd {
  padding: 0 40rpx;
  background-color: white;
}

.userinfo {
  display: block;
}

.info-item {
  display: block;
}

.userinfo-img {
  width: 130rpx;
  height: 130rpx;
  padding: 35prx 0;
}

.userinfo-avatar {
  width: 128rpx;
  height: 128rpx;
  float: left;
  margin-top: 35rpx;
  margin-right: 20rpx;
  vertical-align: middle;
  overflow: hidden;
}
.userinfo-avatar-img{
  width: 70%;
  height: 70%;
}
.userinfo-name {
  /* padding-top: 20rpx;
  display: block;
  line-height: 40rpx; */
  display: inline-block;
  width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
 /*font-size: 28rpx;*/
.head {
  height: 200rpx;
  line-height: 200rpx;
  vertical-align: middle;
  margin: 50rpx 0 50rpx 0;
}
.total-wrapper{
  padding:20rpx 40rpx;
  background: #fff;
  margin-bottom: 10rpx;
}
.total{
  line-height: 60rpx;
}
.button {
  margin-top: 30rpx;
  width: 90%;
}

.text_yue {
  margin-top: 20rpx;
  float: left;
  line-height: 80rpx;
}

.value_yue {
  float: left;
  margin-top: 20rpx;
  font-size: 55rpx;
  margin-left: 10rpx;
}

.text_tixian {
  margin-top: 10rpx;
  float: right;
  line-height: 80rpx;
}

.arrow_right {
  width: 30rpx;
  height: 30rpx;
}

.midddle {
  margin-top: 100rpx;
  background-color: white;
}

.middle view {
  overflow: hidden;
  line-height: 80rpx;
}

.middle view image {
  width: 40rpx;
  height: 40rpx;
}

.middle view span {
  float: left;
  margin-left: 10rpx;
}

.middle view label {
  float: right;
}

.me_list {
  display: flex;
  align-items: center;
  /* padding-top: 20rpx; */
  height: 100rpx;
  border-bottom: 1px solid #eeedf3;
}
.me_list_text {
  flex: 1;
}
.button {
  margin-top: 200rpx;
}
.logout{
  margin-top: 40rpx;
}
.text_userid{
  margin-top: 10rpx;
   display: block;
}
.customer-service{
  background: none;
  border:none;
  line-height: 80rpx;
  text-align: left;
  color: #000;
  font-size: 15px;
  text-indent: 20rpx;
  padding: 0;
  margin: 0;
}
.customer-service::after{
  border:none;
}
.logout{
  margin-top:50rpx;
  margin-bottom: 50rpx;
}