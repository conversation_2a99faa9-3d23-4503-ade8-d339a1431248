/**app.wxss**/
/*page {  
  display: block;  
  min-height: 100%;  
  background-color: #eeedf3;  
}  */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 

/* 以下是自定义的dialog 输入密码*/
/*mask*/
.drawer_screen {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  background: #000;
  opacity: 0.5;
  overflow: hidden;
}

/*content*/
.drawer_box {
  width: 650rpx;
  overflow: hidden;
  position: fixed;
  top: 50%;
  left: 0;
  z-index: 1001;
  background: #FAFAFA;
  margin: -150px 50rpx 0 50rpx;
  border-radius: 3px;
}

.drawer_title{
  padding:15px;
  text-align: center;
}
.drawer_content {
  height: 120rpx;
  overflow-y: scroll; /*超出父盒子高度可滚动*/
}

.btn_ok{
  padding: 10px;
  text-align: center;
  border-top: 1px solid #E8E8EA;
  color: #3CC51F;
}

.top{
	padding-top:8px;
}
.bottom {
	padding-bottom:8px;
}
.title {
	height: 30px;
	line-height: 30px;
	width: 50rpx;
	text-align: center;
	display: inline-block;
	font: 300 28rpx/30px "microsoft yahei";
}

.input_base {
	border: 2rpx solid #ccc;
	padding-left: 10rpx;
	margin-right: 50rpx;
}
.input_h30{
	height: 30px;
	line-height: 30px;
}
.input_h60{
	height: 60px;
}
.grid { display: -webkit-box; display: box; }
.col-0 {-webkit-box-flex:0;box-flex:0;}
.col-1 {-webkit-box-flex:1;box-flex:1;}
.fl { float: left;}
.fr { float: right;}
/* 以上是自定义的dialog 输入密码*/