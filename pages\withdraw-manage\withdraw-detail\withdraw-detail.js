//index.js
//获取应用实例
var app = getApp()
var utils = require('../../../utils/util.js');
Page({
  data: {
    scrollHeight: 0,
    scrollTop: 0,
    hidden: false,
    hiddenin: true,
    page: 1,
    size: 20,
    motto: 'Hello World1',
    userInfo: {},
    berths: [],
    info: [],
    result: null,
    orderNum: 0,
    orderTotal: 0,
    totalCount: 0,
    loadTime: 0,
    cashDetail: [
      {
        typeName: '自动提现',
        types: 2,
        yue: 0,
        date: '2017-4-1 12:45:22',
        cash: 2500
      },
      {
        typeName: '每日结算',
        types: 1,
        yue: 2500,
        date: '2017-4-1 00:00:22',
        cash: 500
      }
    ],
    isShowEmpty: false,
    timespan: 0
  },
  //下拉刷新
  onPullDownRefresh: function () {

  },
  //滚动触发
  scroll: function (event) {
    // this.setData({
    //   scrollTop: event.detail.scrollTop
    // });
  },
  //滑动到顶部刷新
  scrolltop: function (e) {
    let that = this;
    let timespan = new Date().getTime() - that.data.currentTime;
    that.data.currentTime = new Date().getTime();
    if (timespan > 2000) {
      // that.setData({
      //   page: 1,
      //   info: [],
      //   hidden: false
      // })
      loadData(20, 1, this);
    } else {

    }

    console.log("滑到到顶部了" + that.data.page);
  },
  clickFresh: function (e) {
    var that = this;
    that.setData({
      page: 1,
      info: [],
      hidden: false
    })
    loadData(this.data.size, this.data.page, this);
  },
  //滑动到底部触发加载更多
  scrollbottom: function (e) {
    var that = this;
    if (utils.oneSecondClick(that.data.loadTime)) {
      if ((that.data.totalCount == that.data.info.length) && that.data.totalCount > 0) {
          // 已加载完所有数据
        wx.showToast({
          title: '下面没有了',
          icon: 'success',
          duration: 2000
        })
      } else {
        that.setData({
          page: that.data.page + 1,
          hiddenin: false
        })
        loadData(this.data.size, this.data.page, this);
      }
    } else {
      that.setData({
        loadTime: Date.now()
      })
    }
  },
  onLoad: function () {
    var that = this
    wx.getSystemInfo({
      success: function (res) {
        that.setData({
          scrollHeight: res.windowHeight
        })
      },
      fail:function(error){
        that.setData({
          scrollHeight: 600
        })
      }
    })

  },
  onShow: function () {
    wx.showLoading({
      title: '加载中...',
      mask: true,
    })
    this.setData({
      size: 20,
      page: 1,
      info: [],
    })
    loadData(20, 1, this);
  }

})

function loadData(size, page, that) {
  wx.showLoading({
    title: '加载中...',
    mask: true,
  })
  let token;
  // let parkid;
  wx.getStorage({
    key: 'token',
    success: function (res) {
      // success
      token = res.data;
      wx.request({
        url: app.globalData.url + '/trade/withdrawrecord',
        data: {
          page: page,
          rp: size,
          token: token,
          otype: 1
        },
        method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        }, // 设置请求的 header
        success: function (res) {
          // success
          if (res.statusCode == 200) {
            that.setData({
              hidden: true,
              hiddenin: true,

            })
            wx.hideLoading()
            if (res.data.validate == 1) {
              console.log("token is invalid validate==1")
              wx.redirectTo({
                url: '../login/login',
                success: function (res) {
                  // success
                },
                fail: function () {
                  // fail
                },
                complete: function () {
                  // complete
                }
              })
            } else {
              that.data.totalCount = res.data.total;
              if (that.data.totalCount > that.data.info.length) {
                let arrs = res.data.rows;
                let arrs2 = [];
                for (let item of arrs) {
                    item.ctime = utils.formatTime(new Date(item.ctime * 1000))
                    item.money = item.money.toFixed(2)
                    arrs2.push(item)
                }
                that.setData({
                  orderNum: res.data.count,
                  orderTotal: res.data.money,
                  info: that.data.info.concat(arrs2),
                  loadTime: Date.now()
                });
              }
              if (res.data.rows.length == 0 && that.data.totalCount > 0) {
                wx.showToast({
                  title: '下面没有了',
                  icon: 'success',
                  duration: 2000
                })
              }
              if (res.data.total == 0) {
                that.setData({
                  isShowEmpty: false
                })
              } else {
                that.setData({
                  isShowEmpty: true
                })
              }
            }
          } else {
            wx.hideLoading()
            utils.alertDialog('请求错误' + res.statusCode)
          }


        },
        fail: function () {
          // fail
          // utils.reLogin();
          wx.hideLoading()
          utils.network("网络连接不可用")
          that.setData({
            hidden:true
          })
        },
        complete: function () {
          // complete
        }
      })

    },
    fail: function () {
      // fail
      // utils.reLogin();
      utils.network("网络连接不可用")
    },
    complete: function () {
      // complete
    }
  })
}
