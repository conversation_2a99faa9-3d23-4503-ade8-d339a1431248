
<!--pages/me/me.wxml-->
<view class="holder">
  <view>
    <view class="head padd">
      <view class="userinfo-avatar">
        <image class="userinfo-avatar-img" src="{{avatarUrl}}"></image>
      </view>
      <text class="userinfo-name">{{parkInfo.name}}</text>
      <text class="text_tixian" bindtap="clicktixian" hidden='true'>提现 ></text>
    </view>
    <view class='total-wrapper'>
      <view class='total'>今日电子收入:<text>{{parkInfo.elec_total?parkInfo.elec_total:'0'}}</text> 元</view>
      <view class='total'>可提现  :<text>{{parkInfo.balance}}</text> 元</view>
    </view>
    <view class='middle padd'>
      <view class="me_list" bindtap="clickdetail">
        <image src="/image/me_detail.png"></image>
        <span class="me_list_text">提现管理</span> 
        <label>></label>
      </view>
       <view class="me_list" bindtap="clickcommonpay">
        <image src="/image/me_compay.png"></image>
        <span class="me_list_text">我要收款</span> 
        <label>></label>
      </view>
      <view class="me_list" bindtap="clickCustomerService">
        <image src="/image/me_us.png"></image>
        <button open-type="contact" class="customer-service me_list_text">在线客服</button>
        <label>></label>
      </view>
      <!-- <view class="me_list" bindtap="clickus">
        <image src="/image/me_us.png"></image>
        <span class="me_list_text">关于我们</span> 
        <label>></label>
      </view> -->
    </view>
    <button class="button padd logout" hover-class="button-hover" size="80%" type="primary" bindtap="clicklogout" loading="{{loading}}">
      退出登录
    </button>
  </view>
</view>