var app = getApp()
var utils = require('../../utils/util.js');
Page({
  data: {
    // String1
    formId: "",
    input0: "",
    input1: "",
    access_token: "",
    yue: 0,
    motto: 'Hello World3',
    userInfo: {},
    name: 'wechat',
    ischange: false,
    nickName: '',
    parkInfo: {},
    parkid: '',
    avatarUrl:'../../image/photo-icon.png'
  },
  clickCustomerService:function(){

  },
  clicktixian: function (e) {
    wx.navigateTo({
      url: '../cash/cash',
      success: function (res) {
        // success
      },
      fail: function () {
        // fail
      },
      complete: function () {
        // complete
      }
    })
  },
  clickdetail: function (e) {
    //console.log("点击了提现明细")
    wx.navigateTo({
      url: '../withdraw-manage/withdraw-manage',
      success: function (res) {
        // success
      },
      fail: function () {
        // fail
      },
      complete: function () {
        // complete
      }
    })
  },
  clickbank: function (e) {
    //console.log("点击了银行账户")
    wx.navigateTo({
      url: '../bankaccount/bankaccount',
      success: function (res) {
        // success
      },
      fail: function () {
        // fail
      },
      complete: function () {
        // complete
      }
    })
  },
  clickcommonpay: function (e) {
    // //console.log("点击了银行账户")
    wx.navigateTo({
      url: '../commonpay/commonpay',
      success: function (res) {
        // success
      },
      fail: function () {
        // fail
      },
      complete: function () {
        // complete
      }
    })
  },
  clickus: function (e) {
    //console.log("点击了关于我们")
    wx.navigateTo({
      url: '../aboutus/aboutus',
      success: function (res) {
        // success
      },
      fail: function () {
        // fail
      },
      complete: function () {
        // complete
      }
    })
  },
  clicklogout: function (e) {
    utils.reLogin();
  },
  formReset: function () {
    //console.log('form发生了reset事件')
  },
  onLoad: function (options) {
    let that = this;
    wx.getStorage({
      key: 'user',
      success: function (res) {
        // success
        that.setData({
          nickName: res.data.nickname,
          parkid: res.data.parkid
        });
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
  onReady: function () {
    // 生命周期函数--监听页面初次渲染完成
    // String3
  },
  onShow: function () {
    // 生命周期函数--监听页面显示
    // String4
    let token;
    let that = this;
    wx.getStorage({
      key: 'token',
      success: function (res) {
        // success
        token = res.data;
        wx.request({
          url: app.globalData.url + '/getdata/getparkinfo',
          data: {
            token: token,
          },
          method: 'GET', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          }, // 设置请求的 header
          success: function (res) {
            // success
            if (res.statusCode == 200) {
              if (res.data.validate != undefined) {
                utils.networkType()
              } else {
                let parkinfotemp = res.data;
                parkinfotemp.balance = parkinfotemp.balance?parkinfotemp.balance.toFixed(2):'0.00'
                parkinfotemp.elec_total = parkinfotemp.elec_total?parkinfotemp.elec_total.toFixed(2):'0.00'
                that.setData({
                  parkInfo: parkinfotemp
                  
                })
              }
            } else {
              utils.alertDialog('请求错误' + res.statusCode)
            }

          },
          fail: function (res) {
            utils.networkType()
          }
        })
      },
      fail: function (res) {
        utils.networkType()
      },
      complete: function (res) {
        // complete
      }
    })

  },
  onHide: function () {
    // 生命周期函数--监听页面隐藏
    // String5
  },
  onUnload: function () {
    // 生命周期函数--监听页面卸载
    // String6
  },
  onPullDownRefresh: function () {
    // 页面相关事件处理函数--监听用户下拉动作
    // String7
  },
  onReachBottom: function () {
    // 页面上拉触底事件的处理函数
    // String8
  },
  onShareAppMessage: function () {
    // 用户点击右上角分享
    return {
      title: '泊链科技', // 分享标题
      desc: '深圳泊链科技有限公司', // 分享描述
      path: '/pages/me/me' // 分享路径

    }
  }
})