/**index.wxss**/
.mask{
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9999
}
.container {
  height: 100%;
  padding: 0 20rpx;
}

.flex-wrp {
  width: 100%;
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #62b900;
}

.flex-item {
  width: 100%;
  height: 50rpx;
  color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.listitem {
  width: 95%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.borderline label {
  width: 100%;
  float: left;
  height: 1px;
  border-bottom: 1px solid #62b900;
}

.item_line1 {
  display: flex;
  flex-direction: row;
}

.item_line1 .carnumber {
  flex: 3;
  justify-content: flex-start;
  margin-top: 30rpx;
}

.item_line1 .time {
  margin-top: 30rpx;
  flex: 2;
  display: flex;
  justify-content: flex-end;
}

.item_line2 {
  padding-top: 10rpx;
  display: flex;
  flex-direction: row;
  margin-top: 20rpx;
  padding-bottom: 10rpx;
}

.item_line2 .orderid {
  flex: 3;
  justify-content: flex-start;
  color: #7c7c7c;
}

.item_line2 .total {
  flex: 2;
  display: flex;
  justify-content: flex-end;
   color: #7c7c7c;
}

.refresh {
  width: 100%;
  text-align: center;
  margin-top: 450rpx;
}
