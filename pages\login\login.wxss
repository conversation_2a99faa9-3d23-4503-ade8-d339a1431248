/* page/index/index.wxss */
.inputclass{
    margin-left: 40rpx;
   }
.inputs{
    margin-top: 20rpx;
    padding-left: 10rpx;
    border-bottom: 1px solid #62b900;
    width: 90%;
    height: 100rpx;
}
.button{
    margin-top: 50rpx;
    width: 90%
}
.forget{
    margin-top: 20rpx;
    float: right;
    margin-right: 40rpx
}
.tips{
      margin-top: 20rpx;
    float: right;
    margin-right: 40rpx;
    color: #7c7c7c;
    font-size: 30rpx;
    padding-bottom: 20rpx;
}
.views {
    width: 100%;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 30rpx 0;
 
}
.image {
  width: 150rpx;
  height: 150rpx;
}
.optionview{
  padding-left: 40rpx;
  display: block;
  width: 100%;
    margin-top: 40rpx;
    padding-bottom: 10rpx;
}
.opt_regis{
  width: 200rpx;
  color: #62b900;
  float: left;
  height: 80rpx;
}
.opt_reset{
  height: 80rpx;
  width: 200rpx;
  color: #62b900;
  /* margin-right: 70rpx; */
  float: right;
}
.remember-pass{
  /* font-size: 34rpx; */
  margin:40rpx 0 40rpx 40rpx;
}
.remember{
  display: inline-block;
}
.user-image{
  width: 30rpx;
  height: 30rpx;
}

