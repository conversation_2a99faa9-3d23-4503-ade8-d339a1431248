// pages/commonpay/commonpay.js
var md5 = require('../../utils/md5.js');
var utils = require('../../utils/util.js');
// 获取全局应用程序实例对象
var app = getApp();
var QR = require("../../utils/qrcode.js");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    hideloading: true,
    titlePlaceHolder: '请输入收款类别',
    amountPlaceHolder: '请输入收款金额',
    desPlaceHolder: '请输入备注',
    wxType: 'default',
    aliType: 'default',

    titlecontent: '',
    titlecontent_: '',
    amountcontent: '',
    amountcontent_: '',
    descontent: '',
    descontent_: '',

    maskHidden: true,
    imagePath: '',
    placeholder: 's.bolink.club',

    payUrl: '',
    requestok: false
  },
  inputTitle: function (res) {
    let title = res.detail.value
    if (title.length > 20) {
      utils.alertDialog('长度超出限制！')
      title = title.substr(0, title.length - 1)
    }
    this.setData({
      titlecontent_: title,
      titlecontent: encodeURI(title)
    })
  },
  inputAmount: function (res) {
    this.setData({
      amountcontent: res.detail.value
    })
  },
  inputDes: function (res) {
    this.setData({
      descontent: encodeURI(res.detail.value)
    })
  },
  clickWX: function () {
    let that = this
    that.setData({
      wxType: 'primary',
      aliType: 'default'
    })

    requestPayUrl(that, 'weixin')
  },
  clickALI: function () {
    let that = this
    that.setData({
      wxType: 'default',
      aliType: 'primary'
    })
    requestPayUrl(that, 'alipay')

  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // var size = this.setCanvasSize();//动态设置画布大小
    // var initUrl = "http://" + this.data.placeholder;
    // this.createQrCode(initUrl, "mycanvas", size.w, size.h);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
// requestPayUrl: function(that, payType) {}
function requestPayUrl(that, payType) {
  if (that.data.titlecontent == '') {
    utils.alertDialog('请输入收款类别')
    return
  }
  if (that.data.amountcontent == '') {
    utils.alertDialog('请输入收款金额')
    return
  }

  let unionid = wx.getStorageSync('unionid')
  let parkid = wx.getStorageSync('parkid')
  let timestamp = Date.parse(new Date()) / 1000;
  let paytype = wx.getStorageSync('paytype')

  // let titlecontent = encodeURI('测试支付')
  // let amountcontent = 0.01
  // let descontent = encodeURI('收银台支付测试')
  let channelcontent = payType

  let datacontent = '{"title":"' + that.data.titlecontent + '","park_id":"' + parkid + '","amount":"' + that.data.amountcontent + '","pay_type":' + paytype + ',"time_temp":"' + timestamp + '","description":"' + that.data.descontent + '","trade_no":"' + timestamp + timestamp + '","channel":"' + channelcontent + '"}key=' + wx.getStorageSync('ukey') + ''

  // console.log("加密前的datacontent:" + datacontent);
  let signcontent = md5.hexMD5(datacontent).toUpperCase()
  // console.log("加密后的signcontent:" + signcontent);

  let requestUrl = '{"union_id":' + unionid + ',"sign":"' + signcontent + '","data":' + datacontent + '}'
  console.log(requestUrl)
  that.setData({
    hideloading: false
  })
  wx.request({
    url: app.globalData.payurl,
    data: {
      union_id: unionid,
      sign: signcontent,
      data: {
        title: that.data.titlecontent,
        park_id: parkid,
        amount: "" + that.data.amountcontent,
        pay_type: paytype,
        time_temp: timestamp + '',
        description: that.data.descontent,
        trade_no: "" + timestamp + timestamp,
        channel: channelcontent
      }
    },
    method: 'POST', // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
    header: {
      'content-type': 'application/json'
    }, // 设置请求的 header
    success: function (res) {
      that.setData({
        hideloading: true,
        requestok: false
      })
      let size = setCanvasSize();//动态设置画布大小
      if (res.data.state == 1) {
        createQrCode(res.data.payurl, "mycanvas", size.w, size.h, that);
      } else {
        utils.alertDialog(res.data.errmsg)
        QR.qrApi.clear(res.data.payurl, "mycanvas", size.w, size.h);
        // that.setData({
        //   requestok:true
        // })
      }
    },
    fail: function (res) {
      that.setData({
        hideloading: true
      })
      utils.alertDialog('请求失败，请重试')
    }
  })
}

//适配不同屏幕大小的canvas
function setCanvasSize() {
  var size = {};
  try {
    var res = wx.getSystemInfoSync();
    var scale = 750 / 684;//不同屏幕下canvas的适配比例；设计稿是750宽
    var width = res.windowWidth / scale;
    var height = width;//canvas画布为正方形
    size.w = width;
    size.h = height;
  } catch (e) {
    // Do something when catch error
    console.log("获取设备信息失败" + e);
  }
  return size;
}
function createQrCode(url, canvasId, cavW, cavH, that) {
  //调用插件中的draw方法，绘制二维码图片
  QR.qrApi.draw(url, canvasId, cavW, cavH);

  //二维码生成之后调用canvasToTempImage();延迟3s，否则获取图片路径为空
  var st = setTimeout(function () {
    canvasToTempImage(that);
    clearTimeout(st);
  }, 3000);

}
//获取临时缓存照片路径，存入data中
function canvasToTempImage(that) {

  wx.canvasToTempFilePath({
    canvasId: 'mycanvas',
    success: function (res) {
      var tempFilePath = res.tempFilePath;
      console.log(tempFilePath);
      that.setData({
        imagePath: tempFilePath,
      });
    },
    fail: function (res) {
      console.log(res);
    }
  });
}
// //点击图片进行预览，长按保存分享图片
// function previewImg(e) {
//   var img = this.data.imagePath
//   wx.previewImage({
//     current: img, // 当前显示图片的http链接
//     urls: [img] // 需要预览的图片http链接列表
//   })
// }
// function formSubmit(e) {
//   var that = this;
//   var url = e.detail.value.url;
//   url = url == '' ? ('http://' + that.data.placeholder) : ('http://' + url);
//   that.setData({
//     maskHidden: false,
//   });
//   wx.showToast({
//     title: '生成中...',
//     icon: 'loading',
//     duration: 2000
//   });
//   var st = setTimeout(function () {
//     wx.hideToast()
//     var size = that.setCanvasSize();
//     //绘制二维码
//     that.createQrCode(url, "mycanvas", size.w, size.h);
//     that.setData({
//       maskHidden: true
//     });
//     clearTimeout(st);
//   }, 2000)

// }