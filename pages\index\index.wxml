<!--index.wxml-->

<view id="header" class="flex-wrp" style="flex-direction:row;">
  <view class="flex-item">交易笔数：{{orderNum}}笔</view>
  <view class="flex-item">交易额：{{orderTotal}}元</view>
</view>
<view class="borderline" />
<view class="refresh" hidden="{{umpty}}" bindtap="clickFresh">暂无数据，点击刷新</view>
<view class="container">
  <!-- <loading bindchange="loadingChange" hidden="{{hidden}}">加载中...</loading> -->
  <scroll-view scroll-top="{{scrollTop}}" scroll-y="true" style="height:{{scrollHeight}}px;" class="list" bindscrolltolower="scrollbottom" bindscroll="scroll" bindscrolltoupper="scrolltop">
  <!-- <view class='mask' hidden="{{hiddenin}}"style="height:{{scrollHeight}}px;" >
    <loading bindchange="loadingChange">加载中...</loading>
  </view> -->
    <block wx:for="{{info}}" wx:for-index="index" wx:for-item="item" wx:key="index">
      <view class="listitem">
        <view class="item_line1">
          <text class="carnumber" wx:if="{{item.plate_number=='无'}}">{{item.title}}</text>
           <text class="carnumber" wx:else> 车牌号:{{item.plate_number}}</text>
          <text class="time">{{item.total}}元</text>
        </view>
        <view class="item_line2">
          <text class="orderid">订单号:{{item.id}}</text>
          <text class="total">{{item.end_time}}</text>
        </view>

        <view class="borderline">
          <label/>
        </view>
      </view>
    </block>
  </scroll-view>
</view>