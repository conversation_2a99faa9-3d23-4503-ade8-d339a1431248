/* pages/commonpay/commonpay.wxss */

page {
  width: 100%;
  height: 100%;
  background: #f0eff5;
}
.load {
  width: 100%;
  height: 100%;
}
.contain {
  padding: 10rpx;
  display: flex;
  flex-direction: column;
}

.flexitem {
  background: white;
  display: flex;
  flex-direction: row;
  padding: 30rpx;
  height: 60rpx;
 
}
.itemfirst{
  padding-top: 50rpx;
}
.flexitem-btns {
  height: 120rpx;
}

.flexitem text {
  flex: 1;
  line-height: 60rpx;
  text-align: center;
}

.flexitem input {
  padding-left: 10rpx;
  border-bottom: 1rpx solid #62b900;
  flex: 4;
  line-height: 60rpx;
  text-align: center;
}

.flexitem button {
  width: 0;
  flex: 1;
  margin: 15rpx;
  overflow: inherit;
}

.qrcodeLayout {
  background: white;
  padding: 15rpx;
}

.container-box {
  background-color: #efeff4;
}

.img-box {
  background-color: #fff;
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.img-box image {
  width: 750rpx;
  height: 750rpx;
  background-color: #f9f9f9;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
 
  width: 100%;
  height: 100%;
  opacity: 0;
}
