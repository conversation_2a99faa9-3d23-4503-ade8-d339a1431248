/* pages/cashdetail/cashdetail.wxss */

/*.padd {
  padding: 20rpx 40rpx;
}*/
.scroll{
  background-color: white
}
.flex-item {
  display: flex;
  direction: column;
  align-content: center;
  align-items: center;
}
.list{
  padding-left: 5%;
  width: 95%
}
.line1 {
  width: 95%;
  margin-top: 30rpx;
  height: 60rpx;
  display: flex;
  flex-direction: row;
}
.line2{
  width: 95%;
    height: 60rpx;
    border-bottom: 1px solid #62b900;
}
.texttype {
  float: left;
  margin-left: 50rpx;
  flex: 3;
}

.textmoney {
  float: right;
  color: #62b900;
  flex: 1;
}

.textyuan {
  float: right;
  margin-right: 120rpx;
}

.textyue {
  font-size: 30rpx;
  color: #7f7c7c;
  float: left;
  margin-left: 40rpx;
}

.texttime {
  font-size: 30rpx;
  color: #7f7c7c;
  float: right;
  margin-right: 40rpx;
}
.refresh{
  width: 100%;
  text-align: center;
  margin-top: 450rpx;
}
