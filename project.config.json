{"description": "项目配置文件。", "setting": {"urlCheck": false, "es6": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "useMultiFrameRuntime": true, "useApiHook": true}, "compileType": "miniprogram", "libVersion": "3.3.4", "appid": "wx009e96dbfc7b4d74", "projectname": "%E5%81%9C%E8%BD%A6%E5%9C%BA%E6%94%B6%E9%93%B6%E5%8F%B0", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"miniprogram": {"list": [{"name": "扫码", "pathName": "pages/index/index", "query": "", "scene": 1036, "referrerInfo": {}}]}}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}